import { Table } from 'reactstrap';
import Spinner from '../../../components/partials/Spinner';
import { ArticleType } from '../../../types/common/Item';
import ElectronicArticleList from './ElectronicArticleList';
import AllArticleList from './AllArticleList';
import Article, {
    ArticlePageType,
    ArticleQueryRes,
    DeleteArticleRes,
    NewArticleType,
    SearchArticleParam,
} from '../../../types/Article';
import { convertPaging, showToast } from '../../../utils/common';
import { ARTICLES_DELETE } from '../../../services/ArticleService';
import { useState } from 'react';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import { COMMON_MESSAGE } from '../../../constants/common';
import { QueryObserverResult, RefetchOptions } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';

interface IProps {
    activeTab: string;
    displayOptions: Record<string, boolean>;
    isLoading: boolean;
    data: ArticleQueryRes;
    limit: number;
    refetch: (options?: RefetchOptions) => Promise<QueryObserverResult<ArticleQueryRes, Error>>;
    type: ArticlePageType;
    articleTypeId: number;
    isRoyaltiesPage?: boolean;
}

export default function ArticleList({
    activeTab,
    displayOptions,
    isLoading,
    data,
    limit,
    refetch,
    type,
    articleTypeId,
    isRoyaltiesPage = false,
}: Readonly<IProps>) {
    const navigate = useNavigate();
    const articles = data?.articles_list?.data || [];
    const paging = convertPaging<Article, SearchArticleParam>(data.articles_list, limit);
    const [showModalDelete, setShowModalDelete] = useState(false);
    const [articleId, setArticleId] = useState(0);

    const { mutate: deleteArticleMutation, isPending: isDeleting } = useGraphQLMutation(ARTICLES_DELETE, '', {
        onSuccess: () => {
            showToast(true, [COMMON_MESSAGE.SUCCESS_MESSAGE]);
            refetch();
        },
        onError: () => {
            showToast(false, [COMMON_MESSAGE.ERROR_MESSAGE]);
        },
    });

    const onDelete = (id: number) => {
        setArticleId(id);
        setShowModalDelete(true);
    };

    const deleteItem = () => {
        deleteArticleMutation({ id: articleId });
        setShowModalDelete(false);
    };

    const renderTableBody = () => {
        if (articles.length === 0) {
            return (
                <tbody>
                    <tr>
                        <td colSpan={7}>Không có dữ liệu để hiển thị</td>
                    </tr>
                </tbody>
            );
        }

        if (activeTab === NewArticleType.ALL || activeTab === NewArticleType.UNCLASSIFIED) {
            return (
                <AllArticleList
                    articles={articles}
                    paging={paging}
                    onDelete={onDelete}
                    acticlePageType={type}
                    isRoyaltiesPage={isRoyaltiesPage}
                />
            );
        }

        if (
            activeTab === ArticleType.ELECTRONIC.toString() ||
            activeTab === ArticleType.PAPER.toString() ||
            activeTab === ArticleType.TELEVISION.toString() ||
            activeTab === ArticleType.VIDEO.toString()
        ) {
            return (
                <ElectronicArticleList
                    articles={articles}
                    paging={paging}
                    onDelete={onDelete}
                    acticlePageType={type}
                    isRoyaltiesPage={isRoyaltiesPage}
                />
            );
        }

        // Fallback cho các tab khác
        return (
            <tbody>
                <tr>
                    <td colSpan={7}>Không có dữ liệu để hiển thị</td>
                </tr>
            </tbody>
        );
    };

    const renderTableUI = () => (
        <Table responsive striped>
            <thead>
                <tr>
                    <th className="text-center">STT</th>
                    <th>Tiêu đề</th>
                    <th>Thể loại tin</th>
                    <th>Chuyên mục</th>
                    <th>Người biên tập</th>
                    <th className="text-center">Trạng thái</th>
                    <th className="text-center">Xử lý</th>
                </tr>
            </thead>
            {renderTableBody()}
        </Table>
    );

    return (
        <div className="card">
            <div className="card-body">
                {isLoading ? <Spinner /> : renderTableUI()}
                <ModalConfirm
                    show={showModalDelete}
                    text={'Bạn có thực sự muốn xoá tin tức này?'}
                    btnDisabled={isDeleting}
                    changeShow={(s: boolean) => setShowModalDelete(s)}
                    submitAction={deleteItem}
                />
            </div>
        </div>
    );
}
