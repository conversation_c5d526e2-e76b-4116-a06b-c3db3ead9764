import { yupResolver } from '@hookform/resolvers/yup';
import { keepPreviousData, QueryObserverResult, RefetchOptions } from '@tanstack/react-query';
import classNames from 'classnames';
import { format, parseISO } from 'date-fns';
import { groupBy, isEmpty, sortBy } from 'lodash';
import includes from 'lodash/includes';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Clock, DollarSign, Folder, Globe, Image, MessageCircle, RefreshCcw, UploadCloud } from 'react-feather';
import { FormProvider, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { SingleValue } from 'react-select';
import { USER_LIST } from 'services/UserService';
import slugify from 'slugify';
import ArticleFile from 'types/ArticleFile';
import * as yup from 'yup';
import ContentHeader from '../../../components/partials/ContentHeader';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import ModalContent from '../../../components/partials/ModalContent';
import Spinner from '../../../components/partials/Spinner';
import TextEditorTinyMce from '../../../components/partials/TextEditorTinyMce';
import {
    ARTICLE_TAB,
    COMMON_MESSAGE,
    LIMIT_MAX,
    optionModelDefault,
    PAGE_NUMBER_DEFAULT,
    QUERY_KEY,
} from '../../../constants/common';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import {
    ARTICLE_LOGS_LIST,
    ARTICLE_NOTES_CREATE,
    ARTICLE_NOTES_LIST,
    ARTICLES_CHANGE_LOCK,
    ARTICLES_CREATE,
    ARTICLES_UPDATE,
} from '../../../services/ArticleService';
import { checkSpellingErrors, generateTagSuggestions, improveNaturalLanguage } from '../../../services/ChatGptService';
import Article, {
    ArticleDetailQueryRes,
    ArticleFormData,
    ArticlePageType,
    ArticleRoyaltyUser,
    defaultArticleRoyalties,
    RoyaltyType,
    TypeSettingStatus,
} from '../../../types/Article';
import { ArticleLogsQueryRes } from '../../../types/ArticleLog';
import ArticleNote, { ArticleNotesQueryRes } from '../../../types/ArticleNote';
import Category, { CategoryType } from '../../../types/Category';
import { ArticleType, ArticleTypeNames, ItemStatus, SelectOptionModel, TypeMedia } from '../../../types/common/Item';
import Layout from '../../../types/Layout';
import Pseudonym from '../../../types/Pseudonym';
import Tag from '../../../types/Tag';
import Template from '../../../types/Template';
import User, { UserListQuery } from '../../../types/User';
import Workflow from '../../../types/Workflow';
import { WorkflowTransition } from '../../../types/WorkflowPermission';
import cn from '../../../utils/cn';
import { createHierarchy, showToast } from '../../../utils/common';
import ArticleButtonTop from './ArticleButtonTop';
import { CategoryItem } from './CategoryListCheckbox';
import ModalContentEditor from './ModalContentEditor';
import ModalRelatedArticle from './ModalRelatedArticle';
import { UpdateArticleFormTabExchange } from './UpdateArticleFormTabExchange';
import { UpdateArticleFormTabFilesPublish } from './UpdateArticleFormTabFilesPublish';
import { UpdateArticleFormTabFilesUtilities } from './UpdateArticleFormTabFilesUtilities';
import { UpdateArticleFormTabHistory } from './UpdateArticleFormTabHistory';
import { UpdateArticleFormTabInformation } from './UpdateArticleFormTabInformation';
import { UpdateArticleFormTabMedia } from './UpdateArticleFormTabMedia';
import { UpdateArticleFormTabOther } from './UpdateArticleFormTabOther';
import { UpdateArticleFormTabPublishPaper } from './UpdateArticleFormTabPublishPaper';
import { UpdateArticleFormTabRoyalties } from './UpdateArticleFormTabRoyalties';
import { UpdateArticleFormTabSeo } from './UpdateArticleFormTabSeo';
import VideoEditor, { extractVideoUrl } from './VideoEditor';

interface IProps {
    id: number | null;
    article?: Article;
    topics: Category[];
    categories: Category[];
    pseudonyms: Pseudonym[];
    tags: Tag[];
    articleTemplates: Template[];
    layouts: Layout[];
    workflows: Workflow[];
    articleFile?: ArticleFile[];
    workflowTransitions: WorkflowTransition[] | undefined;
    type: ArticlePageType;
    typeId: ArticleType;
    departmentId: number;
    currentUser: User | null;
    refetchArticle?: (options?: RefetchOptions) => Promise<QueryObserverResult<ArticleDetailQueryRes, Error>>;
}

const schema = yup
    .object({
        title: yup.string().typeError(COMMON_MESSAGE.FIELD_REQUIRED).required(COMMON_MESSAGE.FIELD_REQUIRED).trim(),
        content: yup.string().typeError(COMMON_MESSAGE.FIELD_REQUIRED).required(COMMON_MESSAGE.FIELD_REQUIRED).trim(),
        articleRoyalties: yup.array().of(
            yup.object({
                article_royalty_users: yup
                    .array()
                    .test('all-have-user_id-and-percent', 'Tác giả và Tỉ lệ là bắt buộc', (value) => {
                        if (!value || value.length === 0) return true;

                        return value.every((item) => !!item?.user_id && !!item?.percent);
                    })
                    .test('unique-user-id', 'Tác giả không được trùng lặp', (value) => {
                        if (!value || value.length === 0) return true;
                        const userIds = value.map((item) => item?.user_id).filter(Boolean);
                        return new Set(userIds).size === userIds.length;
                    })
                    .test('sum-percent-100', 'Tổng tỉ lệ phải bằng 100', (value) => {
                        if (!value || value.length === 0) return true;
                        const sum = value.reduce((acc, item) => acc + (parseFloat(item?.percent) || 0), 0);
                        return sum === 100;
                    }),
                // Các trường khác của ArticleRoyaltiesPage nếu cần
            })
        ),
    })
    .required();

export default function UpdateArticleForm({
    id,
    article,
    pseudonyms,
    tags,
    articleTemplates,
    topics,
    categories,
    layouts,
    workflows,
    articleFile,
    workflowTransitions,
    type,
    typeId,
    departmentId,
    currentUser,
    refetchArticle,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    const [showConfirm, setShowConfirm] = useState(false);
    const [tabAction, setTabAction] = useState<ARTICLE_TAB | null>(ARTICLE_TAB.INFORMATION);
    const [showModalMedia, setShowModalMedia] = useState(false);
    // tslint:disable-next-line: no-any
    const editorRef = useRef<any>(null);
    const [typeModalMedia, setTypeModalMedia] = useState<TypeMedia>();
    const [template, setTemplate] = useState<Template>();
    const [topicValue, setTopicValue] = useState<SelectOptionModel>(optionModelDefault);
    const [isArticleLocked, setIsArticleLocked] = useState(false);
    const [pseudonymValue, setPseudonymValue] = useState<SelectOptionModel>(optionModelDefault);
    const [tagAdds, setTagAdds] = useState<string[]>([]);
    const [articleNotes, setArticleNotes] = useState<ArticleNote[]>([]);
    const [avatars, setAvatars] = useState({
        avatar1: '',
        avatar2: '',
    });
    const [mainCategoryIds, setMainCategoryIds] = useState<CategoryItem[]>([]);
    const [subCategoryIds, setSubCategoryIds] = useState<CategoryItem[]>([]);
    const [topicId, setTopicId] = useState<number | null>(null);
    const [selectedArticleKindIds, setSelectedArticleKindIds] = useState<number[]>([]);
    const [newArticleNotes, setNewArticleNotes] = useState<string[]>([]);
    const [isLoadingTags, setIsLoadingTags] = useState(false);
    const [showSpellingModal, setShowSpellingModal] = useState(false);
    const [correctedContent, setCorrectedContent] = useState('');
    const [isCheckingSpelling, setIsCheckingSpelling] = useState(false);
    const [improvedContent, setImprovedContent] = useState('');
    const [showNaturalLanguageModal, setShowNaturalLanguageModal] = useState(false);
    const [isImprovingLanguage, setIsImprovingLanguage] = useState(false);
    const [sortCategories, setSortCategories] = useState<Category[]>([]);
    const [showRelatedArticleModal, setShowRelatedArticleModal] = useState(false);
    const [selectedRelatedArticles, setSelectedRelatedArticles] = useState<Article[]>([]);
    const [searchParams] = useSearchParams();

    useEffect(() => {
        if (searchParams.get('tab')) {
            setTabAction(searchParams.get('tab') as ARTICLE_TAB);
        }
    }, [searchParams]);

    // State for publish paper tab
    const [publishPaperData, setPublishPaperData] = useState<{
        selectedPublicationId: number | null;
        selectedIssueId: number | null;
        articleIssuePages: Array<{
            issue_page_id: number;
            display_order: string;
            comment: string;
        }>;
    }>(() => {
        // Initialize from article data when editing
        if (article) {
            return {
                selectedPublicationId: article.press_publication_id ?? null,
                selectedIssueId: article.issue_id ?? null,
                articleIssuePages:
                    article.articleIssuePages?.map((page) => ({
                        issue_page_id: page.issue_page_id,
                        display_order: page.display_order.toString(),
                        comment: page.comment || '',
                    })) || [],
            };
        }
        return {
            selectedPublicationId: null,
            selectedIssueId: null,
            articleIssuePages: [],
        };
    });

    const navigate = useNavigate();

    const canEditArticle = useMemo(() => {
        if (!id || !article) return true;
        if (!article.lock_user_id) return true;
        return article.lock_user_id === currentUser?.id;
    }, [id, article, currentUser]);

    useEffect(() => {
        if (categories) {
            setSortCategories(createHierarchy(categories));
        }
    }, [categories]);

    const articleRelationFilter = useMemo(() => [`article_id:=(${id})`], [id]);

    const { data: articleLogsData } = useGraphQLQuery<ArticleLogsQueryRes>(
        [QUERY_KEY.ARTICLE_LOGS, { page: PAGE_NUMBER_DEFAULT, limit: LIMIT_MAX, filters: articleRelationFilter }],
        ARTICLE_LOGS_LIST,
        {
            page: PAGE_NUMBER_DEFAULT,
            limit: LIMIT_MAX,
            filters: articleRelationFilter,
        },
        '',
        {
            enabled: Boolean(id),
        }
    );

    const form = useForm<Article>({
        resolver: yupResolver(schema),
        defaultValues: {
            content: '',
            article_type_id: typeId,
            department_id: departmentId,
            publish_date: null,
            article_type_copy_ids: [],
            articleRoyalties: defaultArticleRoyalties,
        },
    });
    const { data: userData } = useGraphQLQuery<UserListQuery>(
        [QUERY_KEY.USERS, type],
        USER_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: [`status_id:=(${ItemStatus.ACTIVE})`],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const {
        register,
        handleSubmit,
        reset,
        setValue,
        clearErrors,
        watch,
        formState: { errors },
    } = form;

    useEffect(() => {
        if (article) {
            const royaltyMediaFiles = article?.articleFiles.filter((file) => file.is_royalty);

            const articleRoyaltyFilesByUserIds = Object.keys(groupBy(royaltyMediaFiles, 'createdByUser.id'));

            const articleMediaFileUsers: ArticleRoyaltyUser[] = articleRoyaltyFilesByUserIds.map((id) => ({
                user_id: Number(id),
                percent: '',
                comment: '',
            }));

            const sortedArticleRoyalties = sortBy(article.articleRoyalties, [
                function (royalty) {
                    return royalty.type_id;
                },
            ]).map((r) => {
                const article_royalty_users = r.articleRoyaltyUsers;
                delete r.articleRoyaltyUsers;

                return {
                    ...r,
                    article_royalty_users,
                };
            });

            reset({
                ...article,
                articleRoyalties: defaultArticleRoyalties.map((royalty) => {
                    const matched = sortedArticleRoyalties.find((sorted) => sorted.type_id === royalty.type_id);

                    if (royalty.type_id === RoyaltyType.Media) {
                        royalty.article_royalty_users = articleMediaFileUsers;
                    }

                    return matched ? matched : royalty;
                }),
                publish_date: article.publish_date ? format(parseISO(article.publish_date), 'yyyy-MM-dd HH:mm') : null,
                pseudonym_id: article.pseudonym?.id ?? null,
            });
            setAvatars({
                avatar1: article.avatar1?.file_url ?? '',
                avatar2: article.avatar2?.file_url ?? '',
            });

            if (article.pseudonym_id) {
                setPseudonymValue({
                    label: article.pseudonym.name,
                    value: article.pseudonym.id?.toString() ?? '',
                });
            }
            if (article.articleKinds.length > 0) {
                setSelectedArticleKindIds(article.articleKinds.map((item) => item.article_kind_id));
            }
            if (article.articleTags.length > 0) {
                setTagAdds(article.articleTags.map((item) => item.tag.name));
            }
            if (article.articleCategories.length > 0) {
                const mainCategory: CategoryItem[] = [];
                const subCategory: CategoryItem[] = [];
                article.articleCategories.forEach((item) => {
                    const topicSelected = topics.find((topicItem) => topicItem.id === item.category_id);
                    if (topicSelected) {
                        setTopicId(topicSelected.id!);
                        setTopicValue({
                            label: topicSelected.name,
                            value: topicSelected.id?.toString() ?? '',
                        });
                    }
                    if (item.category.category_type_id === CategoryType.CATEGORY) {
                        if (item.category.is_major) {
                            mainCategory.push({
                                category_id: item.category_id,
                                display_order: item.display_order,
                                is_major: item.is_major || false, // Lấy từ ArticleCategory.is_major
                            });
                        } else {
                            subCategory.push({
                                category_id: item.category_id,
                                display_order: item.display_order,
                                is_major: item.is_major || false, // Lấy từ ArticleCategory.is_major
                            });
                        }
                    }
                });
                setMainCategoryIds(mainCategory);
                setSubCategoryIds(subCategory);
            }
            if (article?.relatedArticles?.length) {
                setSelectedRelatedArticles(article.relatedArticles.map((item) => item.relatedArticle));
            }

            // Set publishPaperData for paper articles
            if (typeId === ArticleType.PAPER) {
                setPublishPaperData({
                    selectedPublicationId: article.press_publication_id ?? null,
                    selectedIssueId: article.issue_id ?? null,
                    articleIssuePages:
                        article.articleIssuePages?.map((page) => ({
                            issue_page_id: page.issue_page_id,
                            display_order: page.display_order.toString(),
                            comment: page.comment || '',
                        })) || [],
                });
            }
        }
    }, [article, reset, topics, typeId]);

    useEffect(() => {
        if (!id) {
            const currentWorkflowTransition = workflowTransitions?.find((item) => !item.from);
            const currentWorkflow = workflows.find((item) => item.id === currentWorkflowTransition?.to);
            if (currentWorkflow) {
                setValue('workflow_id', currentWorkflow?.id ?? 0);
            }
        }
    }, [id, workflows, workflowTransitions, setValue]);

    const configsBtnTab = useMemo(
        () => [
            { name: 'Thông tin', value: ARTICLE_TAB.INFORMATION, icon: Clock },
            { name: 'Xuất bản', value: ARTICLE_TAB.PUBLISH, icon: UploadCloud },
            { name: 'SEO', value: ARTICLE_TAB.SEO, icon: Globe },
            { name: 'Nhuận bút', value: ARTICLE_TAB.ROYALTIES, icon: DollarSign },
            { name: 'Media', value: ARTICLE_TAB.MEDIA, icon: Image },
            { name: 'Ghi chú', value: ARTICLE_TAB.EXCHANGE, icon: MessageCircle },
            { name: 'Lịch sử', value: ARTICLE_TAB.HISTORY, icon: RefreshCcw },
            { name: 'Khác', value: ARTICLE_TAB.OTHER, icon: Folder },
        ],
        []
    );

    const onChangeTabAction = (tab: ARTICLE_TAB | null) => {
        setTabAction(tab);
    };
    const isActiveTabAction = (tab: ARTICLE_TAB | null) => tabAction === tab;

    const handlePreview = () => {
        if (editorRef.current) {
            editorRef.current.onPreview();
        }
    };

    const onChangeTopic = (option: SingleValue<SelectOptionModel>) => {
        setTopicValue(option ?? optionModelDefault);
        setTopicId(Number(option?.value ?? 0));
    };

    const onChangePseudonym = (option: SingleValue<SelectOptionModel>) => {
        setPseudonymValue(option ?? optionModelDefault);
        setValue('pseudonym_id', Number(option?.value ?? 0));
        if (option?.value) clearErrors('pseudonym_id');
    };

    const onAddTag = (tagName: string) => {
        if (!includes(tagAdds, tagName)) {
            setTagAdds([...tagAdds, tagName]);
        }
    };

    const { data: articleNotesData, refetch: refetchArticleNotes } = useGraphQLQuery<ArticleNotesQueryRes>(
        [QUERY_KEY.ARTICLE_NOTES, { page: PAGE_NUMBER_DEFAULT, limit: LIMIT_MAX, filters: articleRelationFilter }],
        ARTICLE_NOTES_LIST,
        {
            page: PAGE_NUMBER_DEFAULT,
            limit: LIMIT_MAX,
            filters: articleRelationFilter,
        },
        '',
        {
            enabled: Boolean(id),
        }
    );
    useEffect(() => {
        if (articleNotesData?.article_notes_list.data) {
            setArticleNotes(articleNotesData.article_notes_list.data);
        }
    }, [articleNotesData]);

    const { mutate: createArticleNote } = useGraphQLMutation<
        { article_notes_create: ArticleNote },
        { body: { article_id: number; content: string } }
    >(ARTICLE_NOTES_CREATE, '', {
        onSuccess: () => {
            refetchArticleNotes();
        },
        onError: () => {
            showToast(false, [COMMON_MESSAGE.ERROR_MESSAGE]);
        },
    });

    const onAddNote = async (note: string) => {
        if (!id) {
            const newArticleNote: ArticleNote = {
                id: 0,
                created_at: new Date().toISOString(),
                article_id: 0,
                content: note,
            };
            setNewArticleNotes([...newArticleNotes, note]);
            setArticleNotes([...articleNotes, newArticleNote]);
        } else {
            createArticleNote({ body: { article_id: id, content: note } });
        }
    };

    const onChangeFileArticle = async (file: File) => {};

    const onChooseTemplate = () => {
        if (template) {
            const currentContent = watch('content') || '';
            const newContent = currentContent + template.content;
            setValue('content', newContent);
            setShowConfirm(false);
            setTemplate(template);
        }
    };

    const onSubmitMedia = (url: string, id?: number) => {
        if (editorRef.current && url && typeModalMedia === 'image') {
            editorRef.current.getEditorRef().insertContent(`<img src=${url} width="50%" height="50%">`);
        }
        if (editorRef.current && url && typeModalMedia === 'avatar1') {
            setValue('avatar1_id', id || 0);
            setAvatars({ ...avatars, avatar1: url });
            setTypeModalMedia(undefined);
        }
        if (editorRef.current && url && typeModalMedia === 'avatar2') {
            setValue('avatar2_id', id ?? 0);
            setAvatars({ ...avatars, avatar2: url });
            setTypeModalMedia(undefined);
        }
        // Handle video frame selection for avatar (from VideoEditor)
        if (typeId === ArticleType.VIDEO && url && id) {
            setValue('avatar1_id', id);
            setAvatars({ ...avatars, avatar1: url });
            setTypeModalMedia(undefined);
        }
        setShowModalMedia(false);
    };

    const handleSubmitType = (_type: 'save' | 'send', workflowId: number) => {
        handleSubmit((data) => {
            setValue('workflow_id', workflowId);
            onSubmitForm({ ...data, workflow_id: workflowId });
        })();
    };

    const handleSuccess = () => {
        showToast(true, [COMMON_MESSAGE.SUCCESS_MESSAGE]);
        navigate(`/article/${type}?article_type_id=${typeId}`);
    };

    const { mutate: createArticleMutation } = useGraphQLMutation<
        { articles_create: Article },
        { body: ArticleFormData }
    >(ARTICLES_CREATE, '', {
        onSuccess: handleSuccess,
    });

    const { mutate: updateArticleMutation } = useGraphQLMutation<
        { articles_update: Article },
        { id: number; body: Partial<ArticleFormData> }
    >(ARTICLES_UPDATE, '', {
        onSuccess: () => {
            if (id && article) {
                changeLockMutation({ id, lock: false });
            }
            handleSuccess();
        },
    });

    const { mutate: changeLockMutation } = useGraphQLMutation<
        { articles_change_lock: boolean },
        { id: number; lock: boolean }
    >(ARTICLES_CHANGE_LOCK, '', {
        onSuccess: (_, variables) => {
            if (variables.lock) {
                setIsArticleLocked(true);
                if (refetchArticle) {
                    refetchArticle();
                }
            } else {
                setIsArticleLocked(false);
            }
        },
    });

    useEffect(() => {
        if (id && article && currentUser && !article.lock_user_id) {
            changeLockMutation({ id, lock: true });
        }
    }, [id, article, currentUser, changeLockMutation]);

    const onSubmitForm = (data: Article) => {
        const {
            id,
            workflow,
            avatar1,
            avatar2,
            lock_at,
            lock_user_id,
            articleKinds,
            pseudonym,
            articleTags,
            articleCategories,
            relatedArticles,
            rootArticle,
            lockUser,
            articleFiles,
            articleNotes,
            articleLogs,
            articleIssuePages,
            pressPublication,
            issue,
            articleRoyalties,
            ...newData
        } = data;
        if (typeId === ArticleType.VIDEO) {
            newData.content = extractVideoUrl(newData.content);
        }
        if (mainCategoryIds.length === 0) {
            showToast(false, ['Vui lòng chọn ít nhất 1 chuyên mục chính']);
            return;
        }

        // Kiểm tra xem có chuyên mục chính được chọn không
        const allCategories = [...mainCategoryIds, ...subCategoryIds];
        const majorCategories = allCategories.filter((cat) => cat.is_major);
        if (majorCategories.length === 0) {
            showToast(false, ['Vui lòng chọn một chuyên mục làm chuyên mục chính']);
            return;
        }
        if (majorCategories.length > 1) {
            showToast(false, ['Chỉ được chọn một chuyên mục làm chuyên mục chính']);
            return;
        }
        if (newData.article_type_copy_ids && newData.article_type_copy_ids.length > 0) {
            newData.article_type_copy_ids = newData.article_type_copy_ids.map((item) => Number(item));
            if (!newData.is_sync) {
                showToast(false, ['Vui lòng chọn đồng bộ thông tin bài viết']);
                return;
            }
        }
        if (newData.is_sync) {
            newData.is_sync = Boolean(Number(newData.is_sync));
            if (newData.article_type_copy_ids && newData.article_type_copy_ids.length === 0) {
                showToast(false, ['Vui lòng chọn loại bài viết để đồng bộ']);
                return;
            }
        }

        const categories = [...mainCategoryIds, ...subCategoryIds];
        if (topicId) categories.push({ category_id: topicId, display_order: 1, is_major: false });
        const formData: ArticleFormData = {
            ...newData,
            slug: slugify(newData.title, {
                lower: true,
                locale: 'vi',
                remove: /[*+~.()'"!:@]/g,
                replacement: '-',
            }),
            article_categories: categories,
            article_tags: tagAdds,
            article_kind_ids: selectedArticleKindIds,
            article_royalties: [],
        };
        if (newArticleNotes.length > 0) formData.new_article_notes = newArticleNotes;
        if (formData.publish_date) {
            const parsedDate = parseISO(formData.publish_date);
            formData.publish_date = parsedDate.toISOString();
        }

        if (typeId === ArticleType.PAPER) {
            formData.press_publication_id = publishPaperData.selectedPublicationId ?? undefined;
            formData.issue_id = publishPaperData.selectedIssueId ?? undefined;
            formData.article_issue_pages = publishPaperData.articleIssuePages.map((page) => ({
                issue_page_id: page.issue_page_id,
                display_order: Number(page.display_order),
                comment: page.comment,
            }));
        }
        if (!formData.is_sync) formData.is_sync = false;

        if (!isEmpty(articleRoyalties)) {
            formData.article_royalties = articleRoyalties.filter((royalty) => !isEmpty(royalty.article_royalty_users));
        }

        if (id) {
            updateArticleMutation({ id, body: formData });
        } else {
            formData.typesetting_status_id = TypeSettingStatus.WAIT;
            createArticleMutation({ body: formData });
        }
    };

    const handleSuggestTags = async () => {
        const title = watch('title') || '';
        const content = watch('content') || '';
        if (!title && !content) {
            showToast(false, ['Vui lòng nhập tiêu đề hoặc nội dung bài viết để gợi ý tag']);
            return;
        }
        setIsLoadingTags(true);
        try {
            const suggestedTags = await generateTagSuggestions(title, content);
            if (suggestedTags.length > 0) {
                const newTags = suggestedTags.filter((tag) => !tagAdds.includes(tag));
                if (newTags.length > 0) {
                    setTagAdds([...tagAdds, ...newTags]);
                    showToast(true, [`Đã thêm ${newTags.length} tag được gợi ý`]);
                } else {
                    showToast(false, ['Không có tag mới được gợi ý']);
                }
            } else {
                showToast(false, ['Không thể tạo gợi ý tag. Vui lòng thử lại sau']);
            }
        } catch {
            showToast(false, ['Đã xảy ra lỗi khi gợi ý tag']);
        } finally {
            setIsLoadingTags(false);
        }
    };

    const handleCheckSpellingError = async (content: string) => {
        if (!content) {
            showToast(false, ['Vui lòng nhập nội dung bài viết!']);
            return;
        }

        setIsCheckingSpelling(true);
        try {
            const result = await checkSpellingErrors(content);
            if (!result.hasErrors) {
                showToast(true, ['Không có lỗi chính tả']);
            } else {
                setCorrectedContent(result.correctedContent);
                setShowSpellingModal(true);
            }
        } catch {
            showToast(false, ['Đã xảy ra lỗi khi kiểm tra chính tả']);
        } finally {
            setIsCheckingSpelling(false);
        }
    };

    const applySpellingCorrections = () => {
        setValue('content', correctedContent);
        if (correctedContent) clearErrors('content');
        setShowSpellingModal(false);
    };

    const handleNaturalLanguage = async (content: string) => {
        if (!content) {
            showToast(false, ['Vui lòng nhập nội dung bài viết!']);
            return;
        }

        setIsImprovingLanguage(true);
        try {
            const result = await improveNaturalLanguage(content);
            if (!result.improved) {
                showToast(true, ['Nội dung đã tối ưu ngôn ngữ tự nhiên']);
            } else {
                setImprovedContent(result.improvedContent);
                setShowNaturalLanguageModal(true);
            }
        } catch {
            showToast(false, ['Đã xảy ra lỗi khi cải thiện ngôn ngữ tự nhiên']);
        } finally {
            setIsImprovingLanguage(false);
        }
    };

    const applyLanguageImprovements = () => {
        setValue('content', improvedContent);
        if (improvedContent) clearErrors('content');
        setShowNaturalLanguageModal(false);
    };

    const handleSelectRelatedArticles = (selectedArticles: Article[]) => {
        setValue(
            'related_article_ids',
            selectedArticles.map((item) => item.id!)
        );
        setSelectedRelatedArticles(selectedArticles);
        // setShowRelatedArticleModal(false);
    };

    const hanleResetAvatar1 = useCallback(() => {
        setValue('avatar1_id', null);
        setAvatars((prevState) => ({ ...prevState, avatar1: '' }));
    }, [setValue]);

    return (
        <FormProvider {...form}>
            {(isCheckingSpelling || isImprovingLanguage) && (
                <div
                    className="position-absolute w-100 h-100 d-flex justify-content-center align-items-center"
                    style={{
                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                        zIndex: 1050,
                        top: 0,
                        left: 0,
                    }}
                >
                    <Spinner />
                </div>
            )}
            <form>
                <div className="flex gap-x-2">
                    <button
                        type="button"
                        className="w-[90px] h-[36px] self-center px-[1.25rem] py-[.5rem]  text-[#A42C48FF] border-r border-[#d8d6de]"
                        onClick={() => {
                            if (id && article && currentUser?.id === article.lock_user_id) {
                                changeLockMutation({ id, lock: false });
                            }
                            navigate(`/article/${type}?article_type_id=${typeId}`);
                        }}
                    >
                        Trở về
                    </button>
                    <ContentHeader
                        title={article ? 'Biên tập tin bài' : 'Bài mới'}
                        rightOptions={
                            <ArticleButtonTop
                                isLoading={false}
                                handlePreview={handlePreview}
                                id={id}
                                workflows={workflows}
                                workflowTransitions={workflowTransitions || []}
                                article={article}
                                onSubmitType={handleSubmitType}
                                canEdit={canEditArticle}
                                lockUserName={article?.lockUser?.full_name}
                            />
                        }
                        className={cn('pt-6 pb-2')}
                    />{' '}
                </div>
                <div className="flex gap-x-2">
                    <div
                        className={classNames('w-[380px] flex-shrink-0', {
                            hidden: tabAction !== ARTICLE_TAB.UTILITIES,
                        })}
                    >
                        <div className=" w-[380px]">
                            {tabAction === ARTICLE_TAB.UTILITIES && (
                                <UpdateArticleFormTabFilesUtilities
                                    setTabAction={setTabAction}
                                    articleTemplates={articleTemplates}
                                    layouts={layouts}
                                    setTemplate={setTemplate}
                                    setShowConfirm={setShowConfirm}
                                    watch={watch}
                                    setValue={setValue}
                                />
                            )}
                        </div>
                    </div>
                    <div className="card w-full h-full !mb-0">
                        <div className="card-content">
                            {!id && (
                                <div className="flex border-b mb-2">
                                    {ArticleTypeNames.map((item) => {
                                        const currentPath = window.location.pathname;
                                        const pathParts = currentPath.split('/');
                                        const lastPathPart = pathParts[pathParts.length - 1];
                                        const isActive = lastPathPart === item.id.toString();
                                        const newPath = isActive
                                            ? '#'
                                            : pathParts.slice(0, -1).join('/') + '/' + item.id;

                                        return (
                                            <Link
                                                key={item.id}
                                                to={isActive ? '#' : newPath}
                                                className={classNames('px-3 py-2 text-sm font-medium', {
                                                    'text-[#A42C48FF] border-b-2 border-[#A42C48FF] cursor-default':
                                                        isActive,
                                                    'text-[#565D6DFF] hover:text-[#A42C48FF]': !isActive,
                                                })}
                                                onClick={(e) => {
                                                    if (isActive) e.preventDefault();
                                                }}
                                            >
                                                {t(`${item.name}.single`)}
                                            </Link>
                                        );
                                    })}
                                </div>
                            )}
                            <div className="card-body p-[.75rem]">
                                {typeId === ArticleType.VIDEO ? (
                                    <VideoEditor
                                        initialData={watch('content') || ''}
                                        onEditorChange={(value) => {
                                            setValue('content', value);
                                            if (value) clearErrors('content');
                                        }}
                                        onSubmitMedia={onSubmitMedia}
                                        departmentId={departmentId}
                                        currentAvatarUrl={avatars.avatar1}
                                        hanleResetAvatar1={hanleResetAvatar1}
                                        setTypeModalMedia={setTypeModalMedia}
                                    />
                                ) : (
                                    <TextEditorTinyMce
                                        ref={editorRef}
                                        isUtilities={true}
                                        initialData={watch('content') || ''}
                                        onEditorChange={(value) => {
                                            setValue('content', value);
                                            if (value) clearErrors('content');
                                        }}
                                        setTabAction={setTabAction}
                                        setShowModalMedia={setShowModalMedia}
                                        setTypeModalMedia={setTypeModalMedia}
                                        showModalMedia={showModalMedia}
                                        onSubmitMedia={onSubmitMedia}
                                        isArticle={true}
                                        handleCheckSpellingError={handleCheckSpellingError}
                                        handleNaturalLanguage={handleNaturalLanguage}
                                        setShowRelatedArticleModal={setShowRelatedArticleModal}
                                        articleId={id ?? undefined}
                                        typeModalMedia={typeModalMedia}
                                    />
                                )}
                                <span className="error">{errors.content?.message}</span>
                            </div>
                        </div>
                    </div>
                    <div className="w-[85px] flex-shrink-0">
                        <div className="h-[calc(100vh-266px)] overflow-y-auto overflow-x-hidden">
                            <div className="flex flex-col bg-white w-[75px] card !shadow-none mb-0">
                                {configsBtnTab.map((item, index) => {
                                    if (item.value === ARTICLE_TAB.SEO && typeId === ArticleType.PAPER) return null;
                                    return (
                                        <button
                                            key={index}
                                            type="button"
                                            className="h-[66px] flex flex-col items-center justify-center gap-y-1 hover:bg-[#FCF3F5FF] group"
                                            onClick={() => onChangeTabAction(item.value)}
                                        >
                                            <item.icon
                                                className={classNames('group-hover:text-[#A42C48FF]', {
                                                    'text-[#A42C48FF]': isActiveTabAction(item.value),
                                                })}
                                                size={18}
                                            />
                                            <span
                                                className={classNames(
                                                    'text-[10px] text-[#565D6DFF] group-hover:text-[#A42C48FF] group-hover:font-semibold',
                                                    {
                                                        'text-[#A42C48FF] font-semibold': isActiveTabAction(item.value),
                                                    }
                                                )}
                                            >
                                                {item.name}
                                            </span>
                                        </button>
                                    );
                                })}
                            </div>
                        </div>
                    </div>
                    <div
                        className={classNames('w-[380px] flex-shrink-0', {
                            hidden: tabAction === ARTICLE_TAB.UTILITIES || tabAction === null,
                        })}
                    >
                        <div className="w-[380px]">
                            {tabAction === ARTICLE_TAB.INFORMATION && (
                                // <ScrollTab
                                //     className="card-content max-h-[calc(100vh-200px)] overflow-y-auto overflow-x-hidden"
                                //     onReachBottom={() => {
                                //         setTabAction(ARTICLE_TAB.PUBLISH);
                                //     }}
                                //     onReachTop={() => {
                                //         setTabAction(ARTICLE_TAB.OTHER);
                                //     }}
                                // >
                                <UpdateArticleFormTabInformation
                                    setTabAction={setTabAction}
                                    register={register}
                                    errors={errors}
                                    watch={watch}
                                    setValue={setValue}
                                    pseudonyms={pseudonyms}
                                    onChangePseudonym={onChangePseudonym}
                                    pseudonymValue={pseudonymValue}
                                    topics={topics}
                                    categories={sortCategories}
                                    onChangeTopic={onChangeTopic}
                                    topicValue={topicValue}
                                    setShowModalMedia={setShowModalMedia}
                                    setTypeModalMedia={setTypeModalMedia}
                                    avatars={avatars}
                                    mainCategoryIds={mainCategoryIds}
                                    setMainCategoryIds={setMainCategoryIds}
                                    subCategoryIds={subCategoryIds}
                                    setSubCategoryIds={setSubCategoryIds}
                                    currentTypeId={typeId}
                                    t={t}
                                    id={id}
                                    rootArticle={article?.rootArticle ?? null}
                                    articlePageType={type ?? null}
                                    articleTypeId={typeId ?? null}
                                    setAvatars={setAvatars}
                                />
                                // </ScrollTab>
                            )}
                            {tabAction === ARTICLE_TAB.PUBLISH && (
                                <>
                                    {typeId === ArticleType.PAPER ? (
                                        <UpdateArticleFormTabPublishPaper
                                            setTabAction={setTabAction}
                                            setValue={setValue}
                                            watch={watch}
                                            departmentId={departmentId}
                                            article={article}
                                            publishPaperData={publishPaperData}
                                            setPublishPaperData={setPublishPaperData}
                                        />
                                    ) : (
                                        <UpdateArticleFormTabFilesPublish
                                            setTabAction={setTabAction}
                                            register={register}
                                            onChangeArticleKind={(id: number, checked: boolean) => {
                                                if (checked) {
                                                    setSelectedArticleKindIds([...selectedArticleKindIds, id]);
                                                } else {
                                                    setSelectedArticleKindIds(
                                                        selectedArticleKindIds.filter((item) => item !== id)
                                                    );
                                                }
                                            }}
                                            selectedArticleKindIds={selectedArticleKindIds}
                                        />
                                    )}
                                </>
                            )}

                            {tabAction === ARTICLE_TAB.SEO && (
                                // <ScrollTab
                                //     className="card-content max-h-[calc(100vh-304px)] overflow-y-auto overflow-x-hidden"
                                //     onReachBottom={() => {
                                //         setTabAction(ARTICLE_TAB.ROYALTIES);
                                //     }}
                                //     onReachTop={() => {
                                //         setTabAction(ARTICLE_TAB.PUBLISH);
                                //     }}
                                // >
                                <UpdateArticleFormTabSeo
                                    setTabAction={setTabAction}
                                    tagAdds={tagAdds}
                                    setTagAdds={setTagAdds}
                                    tags={tags}
                                    onAddTag={onAddTag}
                                    onSuggestTags={handleSuggestTags}
                                    isLoadingSuggestion={isLoadingTags}
                                />
                                // </ScrollTab>
                            )}
                            {tabAction === ARTICLE_TAB.ROYALTIES && (
                                // <ScrollTab
                                //     className="card-content overflow-y-auto overflow-x-hidden"
                                //     onReachBottom={() => {
                                //         setTabAction(ARTICLE_TAB.MEDIA);
                                //     }}
                                //     onReachTop={() => {
                                //         setTabAction(ARTICLE_TAB.SEO);
                                //     }}
                                // >
                                <UpdateArticleFormTabRoyalties
                                    setTabAction={setTabAction}
                                    articleFiles={article?.articleFiles ?? []}
                                    users={userData?.users_list.data ?? []}
                                />
                                // </ScrollTab>
                            )}
                            {tabAction === ARTICLE_TAB.MEDIA && (
                                // <ScrollTab
                                //     className="card-content overflow-y-auto overflow-x-hidden"
                                //     onReachBottom={() => {
                                //         setTabAction(ARTICLE_TAB.EXCHANGE);
                                //     }}
                                //     onReachTop={() => {
                                //         setTabAction(ARTICLE_TAB.ROYALTIES);
                                //     }}
                                // >
                                <UpdateArticleFormTabMedia
                                    setTabAction={setTabAction}
                                    articleFiles={article?.articleFiles ?? []}
                                    refetchArticle={refetchArticle}
                                />
                                // </ScrollTab>
                            )}
                            {tabAction === ARTICLE_TAB.EXCHANGE && (
                                // <ScrollTab
                                //     className="card-content max-h-[calc(100vh-304px)] overflow-y-auto overflow-x-hidden"
                                //     onReachBottom={() => {
                                //         setTabAction(ARTICLE_TAB.HISTORY);
                                //     }}
                                //     onReachTop={() => {
                                //         setTabAction(ARTICLE_TAB.MEDIA);
                                //     }}
                                // >
                                <UpdateArticleFormTabExchange
                                    setTabAction={setTabAction}
                                    articleNotes={articleNotes}
                                    onAddNote={onAddNote}
                                />
                                // </ScrollTab>
                            )}
                            {tabAction === ARTICLE_TAB.HISTORY && (
                                // <ScrollTab
                                //     className="card-content max-h-[calc(100vh-304px)] overflow-y-auto overflow-x-hidden"
                                //     onReachBottom={() => {
                                //         setTabAction(ARTICLE_TAB.OTHER);
                                //     }}
                                //     onReachTop={() => {
                                //         setTabAction(ARTICLE_TAB.EXCHANGE);
                                //     }}
                                // >
                                <UpdateArticleFormTabHistory
                                    setTabAction={setTabAction}
                                    articleLogs={articleLogsData?.article_logs_list.data ?? []}
                                    users={userData?.users_list.data ?? []}
                                />
                            )}
                            {tabAction === ARTICLE_TAB.OTHER && typeId === ArticleType.ELECTRONIC && (
                                // <ScrollTab
                                //     className="card-content max-h-[calc(100vh-304px)] overflow-y-auto overflow-x-hidden"
                                //     onReachBottom={() => {
                                //         setTabAction(ARTICLE_TAB.INFORMATION);
                                //     }}
                                //     onReachTop={() => {
                                //         setTabAction(ARTICLE_TAB.HISTORY);
                                //     }}
                                // >
                                <UpdateArticleFormTabOther
                                    onChangeFileArticle={onChangeFileArticle}
                                    setTabAction={setTabAction}
                                />
                                // </ScrollTab>
                            )}
                        </div>
                    </div>
                </div>
            </form>
            <ModalConfirm
                show={showConfirm}
                text={`Bạn có thực sự muốn sử dụng mẫu giao diện "${template?.name}"?`}
                btnDisabled={false}
                changeShow={(s: boolean) => setShowConfirm(s)}
                submitAction={onChooseTemplate}
            />
            <ModalContent
                show={showSpellingModal}
                changeShow={(s: boolean) => setShowSpellingModal(s)}
                title="Kết quả kiểm tra chính tả"
                content={
                    <ModalContentEditor
                        title="Nội dung đã được sửa chính tả:"
                        content={correctedContent}
                        onCancel={() => setShowSpellingModal(false)}
                        onApply={applySpellingCorrections}
                        applyButtonText="Áp dụng chỉnh sửa"
                    />
                }
            />
            <ModalContent
                show={showNaturalLanguageModal}
                changeShow={(s: boolean) => setShowNaturalLanguageModal(s)}
                title="Kết quả cải thiện ngôn ngữ tự nhiên"
                content={
                    <ModalContentEditor
                        title="Nội dung đã được cải thiện:"
                        content={improvedContent}
                        onCancel={() => setShowNaturalLanguageModal(false)}
                        onApply={applyLanguageImprovements}
                        applyButtonText="Áp dụng cải thiện"
                    />
                }
            />
            <ModalContent
                modalSize={'xl'}
                show={showRelatedArticleModal}
                changeShow={(s: boolean) => setShowRelatedArticleModal(s)}
                title="Tin liên quan"
                content={
                    <ModalRelatedArticle
                        onCancel={() => setShowRelatedArticleModal(false)}
                        onApply={handleSelectRelatedArticles}
                        selectedArticles={selectedRelatedArticles}
                        typeId={typeId}
                        id={id}
                        departmentId={departmentId}
                    />
                }
            />
        </FormProvider>
    );
}
