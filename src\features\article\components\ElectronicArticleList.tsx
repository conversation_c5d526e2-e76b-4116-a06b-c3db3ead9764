import { useTranslation } from 'react-i18next';
import { ArticleType, ArticleTypeNames } from '../../../types/common/Item';
import { CategoryType } from '../../../types/Category';
import { genTableIndex } from '../../../utils/common';
import { formatDateTime, FORMAT_DATE } from '../../../utils/date';
import { Trash2, Edit, Eye } from 'react-feather';
import Article, { ArticlePageType } from '../../../types/Article';
import { Paging } from '../../../types/common';
import ArticleCategory from '../../../types/ArticleCategory';
import { Link } from 'react-router-dom';

interface IProps {
    articles: Article[];
    paging: Paging;
    onDelete: (id: number) => void;
    acticlePageType: ArticlePageType;
    isRoyaltiesPage?: boolean;
}

export default function ElectronicArticleList({
    articles,
    paging,
    onDelete,
    acticlePageType,
    isRoyaltiesPage = false,
}: Readonly<IProps>) {
    const { t } = useTranslation();

    const renderActionColumn = (article: Article) => {
        if (isRoyaltiesPage) {
            return (
                <div className="d-flex justify-content-center">
                    <Link to="/" className="btn btn-icon btn-sm btn-flat-primary waves-effect" title="Xem">
                        <Eye size={14} />
                    </Link>
                </div>
            );
        }

        return (
            <div className="d-flex justify-content-center">
                <Link
                    to={`/article/edit/${acticlePageType}/${article.article_type_id}/${article.id}`}
                    className="btn btn-icon btn-sm btn-flat-primary waves-effect me-1"
                    title="Xem"
                >
                    <Edit size={14} />
                </Link>
                <button
                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                    title="Xóa"
                    onClick={() => onDelete(article.id!)}
                >
                    <Trash2 size={14} />
                </button>
            </div>
        );
    };

    return (
        <tbody>
            {articles.map((article, index) => (
                <tr key={article.id}>
                    <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                    <td>
                        <div>{article.title}</div>
                        <div className="text-muted small">{article.pseudonym?.name || 'Chưa có bút danh'}</div>
                        <div className="text-muted small">
                            {article.publish_date
                                ? formatDateTime(article.publish_date, FORMAT_DATE.SHOW_DATE_TIME)
                                : 'Chưa có ngày đăng'}
                        </div>
                    </td>
                    <td>
                        {t(
                            `${
                                ArticleTypeNames.find((type) => type.id === article.article_type_id)?.name ||
                                'unclassified'
                            }.single`
                        )}
                    </td>
                    <td>
                        {article.articleCategories && article.articleCategories.length > 0 ? (
                            article.articleCategories
                                .filter(
                                    (ac: ArticleCategory) => ac.category?.category_type_id === CategoryType.CATEGORY
                                )
                                .map((ac: ArticleCategory) => <div key={ac.id}>{ac.category?.name}</div>)
                        ) : (
                            <div className="text-muted small">Chưa có chuyên mục</div>
                        )}
                    </td>
                    <td>
                        <div>{article.updatedByUser?.full_name || article.createdByUser?.full_name}</div>
                        <div className="text-muted small">
                            Ngày biên soạn:{' '}
                            {formatDateTime(article.updated_at || article.created_at!, FORMAT_DATE.SHOW_DATE_TIME)}
                        </div>
                    </td>
                    <td className="text-center">
                        <span
                            style={{ backgroundColor: article.workflow?.desc || '#000000' }}
                            className={`text-[#fff] badge `}
                        >
                            {article.workflow?.name || 'Chưa có trạng thái'}
                        </span>
                    </td>
                    <td className="text-center">{renderActionColumn(article)}</td>
                </tr>
            ))}
        </tbody>
    );
}
