import { keepPreviousData } from '@tanstack/react-query';
import { ARTICLE_TAB, LIMIT_MAX, QUERY_KEY } from 'constants/common';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { isEmpty } from 'lodash';
import { useMemo, useState } from 'react';
import { X } from 'react-feather';
import { Controller, useFieldArray, useFormContext } from 'react-hook-form';
import { ARTICLE_ROYALTY_TYPE_LIST } from 'services/ArticleRoyaltyTypeService';
import Article from 'types/Article';
import ArticleFile from 'types/ArticleFile';
import { ArticleRoyaltyTypeListQuery } from 'types/ArticleRoyaltyType';
import { ItemStatus, SelectOption } from 'types/common/Item';
import User from 'types/User';
import { BaseModel } from '../../../types/common';
import ArticleRoyaltyItem from './ArticleRoyaltyItem';
import ModalViewUserFiles from './ModalViewUserFiles';

export default interface ArticleType extends BaseModel {
    title: string;
    startValue: number;
    endValue: number;
    defaultValue: number | boolean;
    createdDate: string | null;
    createdBy: string | null;
    updatedDate: string | null;
    updatedBy: string | null;
    scUnitMapId: string;
    status: number | boolean;
}

interface UpdateArticleFormTabRoyaltiesProps {
    setTabAction(tab: ARTICLE_TAB | null): void;
    users: User[];
    articleFiles: ArticleFile[];
}

export const UpdateArticleFormTabRoyalties = ({
    setTabAction,
    users,
    articleFiles,
}: UpdateArticleFormTabRoyaltiesProps) => {
    const { control } = useFormContext<Article>();
    const [isShowUserFilesModal, setIsShowUserFilesModal] = useState(false);
    const [selectedUser, setSelectedUser] = useState<SelectOption | undefined>();

    const { fields } = useFieldArray({
        control,
        name: 'articleRoyalties',
    });

    const userOptions = useMemo(
        () =>
            users?.map((u) => ({
                label: u.full_name,
                value: u.id || 0,
            })) || [],
        [users]
    );

    const userFiles = useMemo(() => {
        if (isEmpty(articleFiles) || isEmpty(selectedUser)) return [];

        return articleFiles
            .filter((f) => f.is_royalty && f?.createdByUser?.id === selectedUser?.value)
            .map((f) => f.file);
    }, [articleFiles, selectedUser]);

    const { data: articleRoyaltyTypeData } = useGraphQLQuery<ArticleRoyaltyTypeListQuery>(
        [QUERY_KEY.ARTICLE_ROYALTY_TYPES],
        ARTICLE_ROYALTY_TYPE_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            filters: [`status_id:=(${ItemStatus.ACTIVE})`],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const articleRoyaltyTypeOptions = useMemo(
        () =>
            articleRoyaltyTypeData?.article_royalty_types_list.data?.map((u) => ({
                label: u.name,
                value: u.id || 0,
            })) || [],
        [articleRoyaltyTypeData]
    );

    const onViewUserFiles = (user?: SelectOption) => {
        if (!user) return;

        setSelectedUser(user);

        setIsShowUserFilesModal(true);
    };

    const onCloseUserFilesModal = (isOpen: boolean) => {
        setIsShowUserFilesModal(isOpen);
        setSelectedUser(undefined);
    };

    return (
        <>
            <div className="card !mb-0">
                <div className="card-header bg-[#FCF3F5FF] p-[.75rem] py-25">
                    <h4 className="card-title !text-base text-[#A42D49FF]">Nhuận bút</h4>
                    <div className="heading-elements">
                        <ul className="mb-0 list-inline">
                            <li>
                                <X
                                    size={24}
                                    className="text-[#A42D49FF] pt-1.5 cursor-pointer"
                                    onClick={() => setTabAction(null)}
                                />
                            </li>
                        </ul>
                    </div>
                </div>
                <div>
                    <div className="card-body p-[.75rem]">
                        {fields.map((royalty, index) => (
                            <Controller
                                key={royalty.type_id}
                                name={`articleRoyalties.${index}`}
                                control={control}
                                render={({ field }) => (
                                    <ArticleRoyaltyItem
                                        typeId={royalty.type_id}
                                        key={royalty.type_id}
                                        setTabAction={setTabAction}
                                        userOptions={userOptions}
                                        articleRoyaltyTypeOptions={articleRoyaltyTypeOptions}
                                        articleRoyaltyFieldName={field.name}
                                        onViewUserFiles={onViewUserFiles}
                                    />
                                )}
                            />
                        ))}
                    </div>
                </div>
            </div>
            <ModalViewUserFiles
                fullName={selectedUser?.label}
                files={userFiles}
                show={isShowUserFilesModal}
                changeShow={onCloseUserFilesModal}
            />
        </>
    );
};
