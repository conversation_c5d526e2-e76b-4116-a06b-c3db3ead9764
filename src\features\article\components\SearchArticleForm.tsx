import { useTranslation } from 'react-i18next';
import SearchForm from '../../../components/partials/SearchForm';
import { convertDataToSelectOptions } from '../../../utils/common';
import Workflow from '../../../types/Workflow';
import Group from 'types/Group';
import { filter } from 'lodash';
import { SearchField } from '../../../types/common/Search';
import User from '../../../types/User';

interface IProps {
    isLoading: boolean;
    workflows: Workflow[];
    articleTypeId: string;
    showDepartment: boolean;
    departments: Group[];
    users: User[];
    isShowUser: boolean;
}

export default function SearchArticleForm({
    isLoading,
    workflows,
    articleTypeId,
    showDepartment,
    departments,
    isShowUser,
    users,
}: Readonly<IProps>) {
    const fields: SearchField[] = [
        { name: 'article_type_id', type: 'hidden', value: articleTypeId, show: true },
        { name: 'title', type: 'text', label: 'Tiêu đề', wrapClassName: 'col-md-4 col-12', show: true },
        {
            name: 'created_group_id',
            type: 'select',
            label: 'Phòng ban',
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: false,
                choices: convertDataToSelectOptions(departments, 'id', 'name', undefined, null),
            },
            show: showDepartment,
        },
        {
            name: 'workflow_id',
            type: 'select',
            label: 'Trạng thái',
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertDataToSelectOptions(workflows, 'id', 'name', undefined, null),
            },
            show: true,
        },
        {
            name: 'created_at__range',
            type: 'date-range',
            wrapClassName: 'col-md-4 col-12',
            show: true,
            options: {
                placeholderText: 'dd/MM/yyyy',
                dateFormat: 'dd/MM/yyyy',
                isClearable: true,
            },
            label: 'Ngày tạo',
        },
        {
            name: 'created_by',
            type: 'select',
            label: 'Người tạo',
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertDataToSelectOptions(users, 'id', 'full_name', undefined, null),
            },
            show: isShowUser,
        },
    ];

    return (
        <SearchForm
            fields={filter(fields, { show: true })}
            isLoading={isLoading}
            preserveParams={['is_unclassified', 'article_type_id']}
        />
    );
}
